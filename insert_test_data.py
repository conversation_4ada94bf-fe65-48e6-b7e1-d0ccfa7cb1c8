#!/usr/bin/env python3
"""
插入测试数据
"""

from config import get_db
from models.sqlalchemy_models import VehicleModelORM, ComponentORM, TestProjectORM, ModalDataORM
from datetime import datetime

def insert_test_data():
    """插入测试数据"""
    db = get_db()
    
    try:
        print("开始插入测试数据...")
        
        # 1. 插入车型数据
        print("插入车型数据...")
        vehicle = VehicleModelORM(
            vehicle_model_code='SGM001',
            vehicle_model_name='测试车型001',
            vin='TEST123456789',
            drive_type='前驱',
            configuration='标准配置',
            production_year=2024,
            status='active'
        )
        
        # 检查是否已存在
        existing_vehicle = db.query(VehicleModelORM).filter(
            VehicleModelORM.vehicle_model_code == 'SGM001'
        ).first()
        
        if not existing_vehicle:
            db.add(vehicle)
            db.flush()
            vehicle_id = vehicle.id
            print(f"插入车型成功，ID: {vehicle_id}")
        else:
            vehicle_id = existing_vehicle.id
            print(f"车型已存在，ID: {vehicle_id}")
        
        # 2. 插入零部件数据
        print("插入零部件数据...")
        component = ComponentORM(
            component_code='COMP001',
            component_name='测试零部件001',
            category='发动机',
            sub_category='缸体',
            description='测试用零部件'
        )
        
        existing_component = db.query(ComponentORM).filter(
            ComponentORM.component_code == 'COMP001'
        ).first()
        
        if not existing_component:
            db.add(component)
            db.flush()
            component_id = component.id
            print(f"插入零部件成功，ID: {component_id}")
        else:
            component_id = existing_component.id
            print(f"零部件已存在，ID: {component_id}")
        
        # 3. 插入测试项目数据
        print("插入测试项目数据...")
        test_project = TestProjectORM(
            vehicle_model_id=vehicle_id,
            component_id=component_id,
            test_status='completed',
            test_date=datetime.now(),
            test_engineer='测试工程师',
            test_condition='标准测试条件',
            excitation_method='锤击法'
        )
        
        existing_project = db.query(TestProjectORM).filter(
            TestProjectORM.vehicle_model_id == vehicle_id,
            TestProjectORM.component_id == component_id
        ).first()
        
        if not existing_project:
            db.add(test_project)
            db.flush()
            project_id = test_project.id
            print(f"插入测试项目成功，ID: {project_id}")
        else:
            project_id = existing_project.id
            print(f"测试项目已存在，ID: {project_id}")
        
        # 4. 插入模态数据
        print("插入模态数据...")
        modal_data_list = [
            {
                'mode_order': 1,
                'direction': 'X',
                'frequency': 125.5,
                'damping_ratio': 0.02,
                'mode_shape_description': '第一阶弯曲模态',
                'notes': '测试数据1'
            },
            {
                'mode_order': 2,
                'direction': 'Y',
                'frequency': 235.8,
                'damping_ratio': 0.03,
                'mode_shape_description': '第二阶扭转模态',
                'notes': '测试数据2'
            },
            {
                'mode_order': 3,
                'direction': 'Z',
                'frequency': 345.2,
                'damping_ratio': 0.025,
                'mode_shape_description': '第三阶复合模态',
                'notes': '测试数据3'
            }
        ]
        
        for data in modal_data_list:
            existing_modal = db.query(ModalDataORM).filter(
                ModalDataORM.test_project_id == project_id,
                ModalDataORM.mode_order == data['mode_order']
            ).first()
            
            if not existing_modal:
                modal_data = ModalDataORM(
                    test_project_id=project_id,
                    mode_order=data['mode_order'],
                    direction=data['direction'],
                    frequency=data['frequency'],
                    damping_ratio=data['damping_ratio'],
                    mode_shape_description=data['mode_shape_description'],
                    notes=data['notes'],
                    updated_by='系统'
                )
                db.add(modal_data)
                print(f"插入模态数据：阶次{data['mode_order']}, 频率{data['frequency']}Hz")
            else:
                print(f"模态数据已存在：阶次{data['mode_order']}")
        
        # 提交所有更改
        db.commit()
        print("所有测试数据插入完成！")
        
        # 验证数据
        print("\n验证插入的数据...")
        vehicle_count = db.query(VehicleModelORM).count()
        component_count = db.query(ComponentORM).count()
        project_count = db.query(TestProjectORM).count()
        modal_count = db.query(ModalDataORM).count()
        
        print(f"车型数量: {vehicle_count}")
        print(f"零部件数量: {component_count}")
        print(f"测试项目数量: {project_count}")
        print(f"模态数据数量: {modal_count}")
        
    except Exception as e:
        print(f"插入数据时出错: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    insert_test_data()
