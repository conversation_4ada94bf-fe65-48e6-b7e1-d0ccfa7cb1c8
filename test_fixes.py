#!/usr/bin/env python3
"""
测试修复后的代码
"""

def test_imports():
    """测试导入是否正常"""
    try:
        from config import get_db, Base
        from utils.response import Result
        from services.modal_service import ModalService
        print("✅ 所有导入成功")
        return True
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_response_methods():
    """测试响应方法"""
    try:
        from utils.response import Result
        
        # 测试成功响应
        success_response = Result.success({"test": "data"}, "测试成功")
        print(f"✅ 成功响应类型: {type(success_response)}")
        
        # 测试错误响应
        error_response = Result.error("测试错误")
        print(f"✅ 错误响应类型: {type(error_response)}")
        
        return True
    except Exception as e:
        print(f"❌ 响应方法测试失败: {e}")
        return False

def test_database_connection():
    """测试数据库连接"""
    try:
        from config import get_db
        
        # 获取数据库会话
        db = get_db()
        print(f"✅ 数据库会话创建成功: {type(db)}")
        
        # 关闭会话
        db.close()
        print("✅ 数据库会话关闭成功")
        
        return True
    except Exception as e:
        print(f"❌ 数据库连接测试失败: {e}")
        return False

def test_service_instantiation():
    """测试服务实例化"""
    try:
        from services.modal_service import ModalService
        
        service = ModalService()
        print(f"✅ 服务实例化成功: {type(service)}")
        
        return True
    except Exception as e:
        print(f"❌ 服务实例化失败: {e}")
        return False

if __name__ == "__main__":
    print("开始测试修复后的代码...")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_response_methods,
        test_database_connection,
        test_service_instantiation
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        print(f"\n运行测试: {test.__name__}")
        if test():
            passed += 1
        print("-" * 30)
    
    print(f"\n测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！代码修复成功！")
    else:
        print("⚠️  部分测试失败，需要进一步检查")
