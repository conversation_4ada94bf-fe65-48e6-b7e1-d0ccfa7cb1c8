<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}NVH数据管理系统{% endblock %}</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/main.css') }}">
    {% block extra_css %}{% endblock %}
</head>
<body>
    <div class="app-container">
        <!-- 顶部导航栏 -->
        <header class="app-header">
            <div class="header-left">
                <h1 class="app-title">NVH数据管理系统</h1>
            </div>
            <div class="header-right">
                <div class="user-info" id="userInfo">
                    <span class="user-name">{{ user.name if user else '用户' }}</span>
                    <div class="user-dropdown">
                        <button class="dropdown-btn">
                            <i class="icon-user"></i>
                            <i class="icon-arrow-down"></i>
                        </button>
                        <div class="dropdown-menu">
                            <a href="#" class="dropdown-item">个人信息</a>
                            <a href="/logout" class="dropdown-item">退出登录</a>
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <div class="app-body">
            <!-- 侧边栏 -->
            <aside class="app-sidebar">
                <nav class="sidebar-nav">
                    <div class="nav-group">
                        <div class="nav-group-title">
                            <i class="icon-database"></i>
                            <span>NVH数据查询</span>
                            <i class="icon-chevron-down group-toggle"></i>
                        </div>
                        <ul class="nav-group-items">
                            <li class="nav-item">
                                <a href="#" class="nav-link" data-module="modal" data-title="模态数据">
                                    <i class="icon-wave"></i>
                                    <span>模态数据</span>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="#" class="nav-link" data-module="sound-quality" data-title="声品质">
                                    <i class="icon-sound"></i>
                                    <span>声品质</span>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="#" class="nav-link" data-module="noise" data-title="噪声">
                                    <i class="icon-noise"></i>
                                    <span>噪声</span>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="#" class="nav-link" data-module="vibration" data-title="振动">
                                    <i class="icon-vibration"></i>
                                    <span>振动</span>
                                </a>
                            </li>
                        </ul>
                    </div>

                    <div class="nav-group">
                        <div class="nav-group-title">
                            <i class="icon-search"></i>
                            <span>其他数据查询</span>
                            <i class="icon-chevron-down group-toggle"></i>
                        </div>
                        <ul class="nav-group-items">
                            <li class="nav-item">
                                <a href="#" class="nav-link" data-module="other" data-title="其他数据">
                                    <i class="icon-file"></i>
                                    <span>其他数据</span>
                                </a>
                            </li>
                        </ul>
                    </div>

                    <div class="nav-group">
                        <div class="nav-group-title">
                            <i class="icon-library"></i>
                            <span>经验数据库</span>
                            <i class="icon-chevron-down group-toggle"></i>
                        </div>
                        <ul class="nav-group-items">
                            <li class="nav-item">
                                <a href="#" class="nav-link" data-module="experience" data-title="经验数据库">
                                    <i class="icon-book"></i>
                                    <span>经验数据库</span>
                                </a>
                            </li>
                        </ul>
                    </div>
                </nav>
            </aside>

            <!-- 主内容区 -->
            <main class="app-main">
                <!-- 标签页区域 -->
                <div class="tab-container">
                    <div class="tab-header" id="tabHeader">
                        <!-- 标签页将通过JavaScript动态添加 -->
                    </div>
                    <div class="tab-content" id="tabContent">
                        <div class="welcome-page" id="welcomePage">
                            <div class="welcome-content">
                                <h2>欢迎使用NVH数据管理系统</h2>
                                <p>请从左侧菜单选择功能模块开始使用</p>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="{{ url_for('static', filename='js/request.js') }}"></script>
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    {% block extra_js %}{% endblock %}
</body>
</html>
