import os
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker


class Config:
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'your-secret-key-here'

    # Keycloak配置
    KEYCLOAK_FRONTEND_CLIENT_ID = 'front'
    KEYCLOAK_FRONTEND_CLIENT_SECRET = 'frontend-secret'
    KEYCLOAK_BACKEND_CLIENT_ID = 'backend'
    KEYCLOAK_BACKEND_CLIENT_SECRET = '8545c061-7cf7-41e5-b92b-e6769a6a75b8'
    KEYCLOAK_SERVER_METADATA_URL = 'https://account-test.sgmw.com.cn/auth/realms/demo/.well-known/openid-configuration'
    KEYCLOAK_CLIENT_KWARGS = {
        'scope': 'openid email profile'
    }

    # 数据库配置
    DB_HOST = 'localhost'
    DB_PORT = 3306
    DB_NAME = 'nvh_data'
    DB_USER = 'root'
    DB_PASSWORD = '123456'

    # 文件上传配置
    UPLOAD_FOLDER = os.environ.get('UPLOAD_FOLDER') or 'uploads'
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB

    # 日志配置
    LOG_LEVEL = os.environ.get('LOG_LEVEL') or 'INFO'
    LOG_FILE = os.environ.get('LOG_FILE') or 'nvh_app.log'


# 简化的数据库连接
DATABASE_URL = f"mysql+pymysql://{Config.DB_USER}:{Config.DB_PASSWORD}@{Config.DB_HOST}:{Config.DB_PORT}/{Config.DB_NAME}?charset=utf8mb4"
engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(bind=engine)
Base = declarative_base()

# 获取数据库会话的函数
def get_db():
    """获取数据库会话"""
    return SessionLocal()
