from typing import Optional, Dict, Any
from sqlalchemy.orm import joinedload
from config import get_db
from models.sqlalchemy_models import VehicleModelORM, ComponentORM, TestProjectORM, ModalDataORM
from utils.response import Result


class ModalService:
    """模态数据服务 - 简化版"""

    def get_vehicles(self):
        """获取所有车型"""
        # 临时返回模拟数据进行测试
        print("DEBUG: 返回模拟车型数据")
        mock_vehicles = [
            {
                'id': 1,
                'vehicle_model_code': 'SGM001',
                'vehicle_model_name': '测试车型001',
                'vin': 'TEST123456789',
                'drive_type': '前驱',
                'configuration': '标准配置',
                'production_year': 2024,
                'status': 'active'
            },
            {
                'id': 2,
                'vehicle_model_code': 'SGM002',
                'vehicle_model_name': '测试车型002',
                'vin': 'TEST987654321',
                'drive_type': '后驱',
                'configuration': '豪华配置',
                'production_year': 2024,
                'status': 'active'
            }
        ]
        return Result.success(mock_vehicles, "获取车型列表成功（模拟数据）")

        # 原始数据库查询代码（暂时注释掉）
        # try:
        #     db = get_db()
        #     vehicles = db.query(VehicleModelORM).filter(
        #         VehicleModelORM.status == 'active'
        #     ).order_by(VehicleModelORM.vehicle_model_name).all()
        #     vehicle_list = [vehicle.to_dict() for vehicle in vehicles]
        #     db.close()
        #     return Result.success(vehicle_list, "获取车型列表成功")
        # except Exception as e:
        #     print(f"DEBUG: 获取车型数据出错: {e}")
        #     return Result.success(mock_vehicles, f"数据库查询出错，返回模拟数据: {e}")

    def get_components(self):
        """获取所有零部件"""
        # 临时返回模拟数据进行测试
        print("DEBUG: 返回模拟零部件数据")
        mock_components = [
            {
                'id': 1,
                'component_code': 'COMP001',
                'component_name': '测试零部件001',
                'category': '发动机',
                'sub_category': '缸体',
                'description': '测试用零部件'
            },
            {
                'id': 2,
                'component_code': 'COMP002',
                'component_name': '测试零部件002',
                'category': '变速箱',
                'sub_category': '齿轮',
                'description': '测试用零部件'
            }
        ]
        return Result.success(mock_components, "获取零部件列表成功（模拟数据）")

        # 原始数据库查询代码（暂时注释掉）
        # try:
        #     db = get_db()
        #     components = db.query(ComponentORM).order_by(
        #         ComponentORM.category,
        #         ComponentORM.sub_category,
        #         ComponentORM.component_name
        #     ).all()
        #     component_list = [component.to_dict() for component in components]
        #     db.close()
        #     return Result.success(component_list, "获取零部件列表成功")
        # except Exception as e:
        #     print(f"DEBUG: 获取零部件数据出错: {e}")
        #     return Result.success(mock_components, f"数据库查询出错，返回模拟数据: {e}")

    def search_by_vehicle(self, vehicle_code: str, component_code: Optional[str] = None):
        """按车型搜索模态数据"""
        # 调试信息：检查输入参数
        print(f"DEBUG: 搜索参数 - vehicle_code: {vehicle_code}, component_code: {component_code}")

        # 临时返回模拟数据进行测试
        if vehicle_code == 'SGM001':
            print("DEBUG: 返回模拟数据进行测试")
            mock_data = [
                {
                    'id': 1,
                    'mode_order': 1,
                    'direction': 'X',
                    'frequency': 125.5,
                    'damping_ratio': 0.02,
                    'mode_shape_description': '第一阶弯曲模态',
                    'mode_shape_file': None,
                    'test_photo_file': None,
                    'vehicle_model_code': 'SGM001',
                    'vehicle_model_name': '测试车型001',
                    'component_code': 'COMP001',
                    'component_name': '测试零部件001',
                    'category': '发动机',
                    'sub_category': '缸体',
                    'test_status': 'completed',
                    'test_date': '2024-08-01',
                    'test_engineer': '测试工程师'
                },
                {
                    'id': 2,
                    'mode_order': 2,
                    'direction': 'Y',
                    'frequency': 235.8,
                    'damping_ratio': 0.03,
                    'mode_shape_description': '第二阶扭转模态',
                    'mode_shape_file': None,
                    'test_photo_file': None,
                    'vehicle_model_code': 'SGM001',
                    'vehicle_model_name': '测试车型001',
                    'component_code': 'COMP001',
                    'component_name': '测试零部件001',
                    'category': '发动机',
                    'sub_category': '缸体',
                    'test_status': 'completed',
                    'test_date': '2024-08-01',
                    'test_engineer': '测试工程师'
                }
            ]
            return Result.success(mock_data, f"搜索到 {len(mock_data)} 条模态数据（模拟数据）")

        # 原始数据库查询代码（暂时注释掉）
        try:
            db = get_db()

            # 调试信息：检查车型是否存在
            vehicle = db.query(VehicleModelORM).filter(
                VehicleModelORM.vehicle_model_code == vehicle_code
            ).first()

            if not vehicle:
                print(f"DEBUG: 未找到车型代码 {vehicle_code}")
                # 显示所有可用的车型代码
                all_vehicles = db.query(VehicleModelORM).all()
                print(f"DEBUG: 数据库中的车型代码: {[v.vehicle_model_code for v in all_vehicles]}")
                db.close()
                return Result.success([], f"未找到车型代码 {vehicle_code}")

            print(f"DEBUG: 找到车型 - ID: {vehicle.id}, 名称: {vehicle.vehicle_model_name}")

            # 构建查询
            query = db.query(ModalDataORM).join(
                TestProjectORM, ModalDataORM.test_project_id == TestProjectORM.id
            ).join(
                VehicleModelORM, TestProjectORM.vehicle_model_id == VehicleModelORM.id
            ).outerjoin(
                ComponentORM, TestProjectORM.component_id == ComponentORM.id
            ).filter(
                VehicleModelORM.vehicle_model_code == vehicle_code
            )

            # 如果指定了零部件，添加条件
            if component_code and component_code not in ['all', 'undefined', None]:
                print(f"DEBUG: 添加零部件过滤条件: {component_code}")
                query = query.filter(ComponentORM.component_code == component_code)

            # 预加载关联数据
            query = query.options(
                joinedload(ModalDataORM.test_project).joinedload(TestProjectORM.vehicle_model),
                joinedload(ModalDataORM.test_project).joinedload(TestProjectORM.component)
            )

            # 排序
            query = query.order_by(
                ComponentORM.category,
                ComponentORM.sub_category,
                ModalDataORM.frequency
            )

            print(f"DEBUG: 执行查询...")
            modal_data_list = query.all()
            print(f"DEBUG: 查询结果数量: {len(modal_data_list)}")
        except Exception as e:
            print(f"DEBUG: 数据库查询出错: {e}")
            db.close()
            return Result.success([], f"数据库查询出错: {e}")

        # 构建返回数据
        result = []
        for md in modal_data_list:
            data = {
                'id': md.id,
                'mode_order': md.mode_order,
                'direction': md.direction,
                'frequency': md.frequency,
                'damping_ratio': md.damping_ratio,
                'mode_shape_description': md.mode_shape_description,
                'mode_shape_file': md.mode_shape_file,
                'test_photo_file': md.test_photo_file,
                'vehicle_model_code': md.test_project.vehicle_model.vehicle_model_code,
                'vehicle_model_name': md.test_project.vehicle_model.vehicle_model_name,
                'component_code': md.test_project.component.component_code if md.test_project.component else None,
                'component_name': md.test_project.component.component_name if md.test_project.component else None,
                'category': md.test_project.component.category if md.test_project.component else None,
                'sub_category': md.test_project.component.sub_category if md.test_project.component else None,
                'test_status': md.test_project.test_status,
                'test_date': md.test_project.test_date.isoformat() if md.test_project.test_date else None,
                'test_engineer': md.test_project.test_engineer
            }
            result.append(data)

        db.close()
        return Result.success(result, f"搜索到 {len(result)} 条模态数据")

    def search_by_component(self, vehicle_code: str, status: Optional[str] = None,
                            order: Optional[str] = None):
        """按零件搜索模态数据"""
        db = get_db()
        # 构建查询
        query = db.query(ModalDataORM).join(
            TestProjectORM, ModalDataORM.test_project_id == TestProjectORM.id
        ).join(
            VehicleModelORM, TestProjectORM.vehicle_model_id == VehicleModelORM.id
        ).outerjoin(
            ComponentORM, TestProjectORM.component_id == ComponentORM.id
        ).filter(
            VehicleModelORM.vehicle_model_code == vehicle_code
        )

        # 添加状态条件
        if status:
            query = query.filter(TestProjectORM.test_status == status)

        # 添加阶次条件
        if order and order != 'all':
            query = query.filter(ModalDataORM.mode_order == int(order))

        # 预加载关联数据
        query = query.options(
            joinedload(ModalDataORM.test_project).joinedload(TestProjectORM.vehicle_model),
            joinedload(ModalDataORM.test_project).joinedload(TestProjectORM.component)
        )

        # 排序
        query = query.order_by(
            VehicleModelORM.vehicle_model_name,
            ComponentORM.category,
            ModalDataORM.frequency
        )

        modal_data_list = query.all()

        # 构建返回数据
        result = []
        for md in modal_data_list:
            data = {
                'id': md.id,
                'mode_order': md.mode_order,
                'direction': md.direction,
                'frequency': md.frequency,
                'damping_ratio': md.damping_ratio,
                'mode_shape_description': md.mode_shape_description,
                'mode_shape_file': md.mode_shape_file,
                'test_photo_file': md.test_photo_file,
                'vehicle_model_code': md.test_project.vehicle_model.vehicle_model_code,
                'vehicle_model_name': md.test_project.vehicle_model.vehicle_model_name,
                'component_code': md.test_project.component.component_code if md.test_project.component else None,
                'component_name': md.test_project.component.component_name if md.test_project.component else None,
                'category': md.test_project.component.category if md.test_project.component else None,
                'sub_category': md.test_project.component.sub_category if md.test_project.component else None,
                'test_status': md.test_project.test_status,
                'test_date': md.test_project.test_date.isoformat() if md.test_project.test_date else None,
                'test_engineer': md.test_project.test_engineer
            }
            result.append(data)

        db.close()
        return Result.success(result, f"搜索到 {len(result)} 条模态数据")

    def get_modal_detail(self, modal_id: int):
        """获取模态数据详情"""
        db = get_db()
        modal_data = db.query(ModalDataORM).options(
            joinedload(ModalDataORM.test_project).joinedload(TestProjectORM.vehicle_model),
            joinedload(ModalDataORM.test_project).joinedload(TestProjectORM.component)
        ).filter(ModalDataORM.id == modal_id).first()

        if not modal_data:
            db.close()
            return Result.not_found("模态数据不存在")

        data = {
            'id': modal_data.id,
            'mode_order': modal_data.mode_order,
            'direction': modal_data.direction,
            'frequency': modal_data.frequency,
            'damping_ratio': modal_data.damping_ratio,
            'mode_shape_description': modal_data.mode_shape_description,
            'mode_shape_file': modal_data.mode_shape_file,
            'test_photo_file': modal_data.test_photo_file,
            'notes': modal_data.notes,
            'vehicle_model_code': modal_data.test_project.vehicle_model.vehicle_model_code,
            'vehicle_model_name': modal_data.test_project.vehicle_model.vehicle_model_name,
            'component_code': modal_data.test_project.component.component_code if modal_data.test_project.component else None,
            'component_name': modal_data.test_project.component.component_name if modal_data.test_project.component else None,
            'category': modal_data.test_project.component.category if modal_data.test_project.component else None,
            'sub_category': modal_data.test_project.component.sub_category if modal_data.test_project.component else None,
            'test_status': modal_data.test_project.test_status,
            'test_date': modal_data.test_project.test_date.isoformat() if modal_data.test_project.test_date else None,
            'test_engineer': modal_data.test_project.test_engineer,
            'test_condition': modal_data.test_project.test_condition,
            'excitation_method': modal_data.test_project.excitation_method
        }

        db.close()
        return Result.success(data, "获取模态数据详情成功")

    def create_modal_data(self, modal_data: Dict[str, Any]):
        """创建模态数据"""
        db = get_db()
        # 创建 SQLAlchemy ORM 对象
        new_modal_data = ModalDataORM(
            test_project_id=modal_data.get('test_project_id'),
            mode_order=modal_data.get('mode_order'),
            direction=modal_data.get('direction'),
            frequency=modal_data.get('frequency'),
            damping_ratio=modal_data.get('damping_ratio'),
            mode_shape_description=modal_data.get('mode_shape_description'),
            mode_shape_file=modal_data.get('mode_shape_file'),
            test_photo_file=modal_data.get('test_photo_file'),
            notes=modal_data.get('notes'),
            updated_by=modal_data.get('updated_by')
        )

        db.add(new_modal_data)
        db.flush()  # 获取 ID
        db.commit()
        db.close()
        return Result.success({'id': new_modal_data.id}, "创建模态数据成功")

    def update_modal_data(self, modal_id: int, modal_data: Dict[str, Any]):
        """更新模态数据"""
        db = get_db()
        # 查找要更新的记录
        existing_modal_data = db.query(ModalDataORM).filter(
            ModalDataORM.id == modal_id
        ).first()

        if not existing_modal_data:
            db.close()
            return Result.not_found("模态数据不存在")

        # 更新字段
        if 'mode_order' in modal_data:
            existing_modal_data.mode_order = modal_data['mode_order']
        if 'direction' in modal_data:
            existing_modal_data.direction = modal_data['direction']
        if 'frequency' in modal_data:
            existing_modal_data.frequency = modal_data['frequency']
        if 'damping_ratio' in modal_data:
            existing_modal_data.damping_ratio = modal_data['damping_ratio']
        if 'mode_shape_description' in modal_data:
            existing_modal_data.mode_shape_description = modal_data['mode_shape_description']
        if 'mode_shape_file' in modal_data:
            existing_modal_data.mode_shape_file = modal_data['mode_shape_file']
        if 'test_photo_file' in modal_data:
            existing_modal_data.test_photo_file = modal_data['test_photo_file']
        if 'notes' in modal_data:
            existing_modal_data.notes = modal_data['notes']
        if 'updated_by' in modal_data:
            existing_modal_data.updated_by = modal_data['updated_by']

        db.commit()
        db.close()
        return Result.success(None, "更新模态数据成功")

    def delete_modal_data(self, modal_id: int):
        """删除模态数据"""
        db = get_db()
        # 查找要删除的记录
        modal_data = db.query(ModalDataORM).filter(
            ModalDataORM.id == modal_id
        ).first()

        if not modal_data:
            db.close()
            return Result.not_found("模态数据不存在")

        db.delete(modal_data)
        db.commit()
        db.close()
        return Result.success(None, "删除模态数据成功")


# 创建服务实例
modal_service = ModalService()
