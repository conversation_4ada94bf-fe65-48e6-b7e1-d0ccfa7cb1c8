/* 重置样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    font-size: 14px;
    line-height: 1.5;
    color: #333;
    background-color: #f5f5f5;
}

/* 应用容器 */
.app-container {
    height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 顶部导航栏 */
.app-header {
    height: 60px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    z-index: 1000;
}

.app-title {
    font-size: 20px;
    font-weight: 600;
    margin: 0;
}

.user-info {
    position: relative;
    display: flex;
    align-items: center;
    gap: 10px;
}

.user-name {
    font-size: 14px;
    font-weight: 500;
}

.user-dropdown {
    position: relative;
}

.dropdown-btn {
    background: rgba(255,255,255,0.1);
    border: none;
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 5px;
    transition: background-color 0.2s;
}

.dropdown-btn:hover {
    background: rgba(255,255,255,0.2);
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    min-width: 120px;
    display: none;
    z-index: 1001;
}

.dropdown-menu.show {
    display: block;
}

.dropdown-item {
    display: block;
    padding: 10px 15px;
    color: #333;
    text-decoration: none;
    transition: background-color 0.2s;
}

.dropdown-item:hover {
    background-color: #f8f9fa;
}

/* 主体区域 */
.app-body {
    flex: 1;
    display: flex;
    overflow: hidden;
}

/* 侧边栏 */
.app-sidebar {
    width: 260px;
    background: #2c3e50;
    color: white;
    overflow-y: auto;
    flex-shrink: 0;
}

.sidebar-nav {
    padding: 20px 0;
}

.nav-group {
    margin-bottom: 10px;
}

.nav-group-title {
    padding: 12px 20px;
    font-weight: 600;
    font-size: 13px;
    color: #bdc3c7;
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    transition: all 0.2s;
}

.nav-group-title:hover {
    background: rgba(255,255,255,0.05);
}

.nav-group-title .group-toggle {
    margin-left: auto;
    transition: transform 0.2s;
}

.nav-group.collapsed .group-toggle {
    transform: rotate(-90deg);
}

.nav-group-items {
    list-style: none;
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.nav-group.collapsed .nav-group-items {
    max-height: 0;
}

.nav-item {
    margin: 0;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 20px 12px 40px;
    color: #ecf0f1;
    text-decoration: none;
    transition: all 0.2s;
    border-left: 3px solid transparent;
}

.nav-link:hover {
    background: rgba(255,255,255,0.1);
    border-left-color: #3498db;
}

.nav-link.active {
    background: rgba(52, 152, 219, 0.2);
    border-left-color: #3498db;
    color: white;
}

/* 主内容区 */
.app-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: white;
    overflow: hidden;
}

/* 标签页容器 */
.tab-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.tab-header {
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    align-items: center;
    min-height: 45px;
    padding: 0 10px;
    overflow-x: auto;
}

.tab-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    background: white;
    border: 1px solid #dee2e6;
    border-bottom: none;
    border-radius: 6px 6px 0 0;
    margin-right: 5px;
    cursor: pointer;
    white-space: nowrap;
    transition: all 0.2s;
    position: relative;
}

.tab-item:hover {
    background: #f8f9fa;
}

.tab-item.active {
    background: white;
    border-bottom: 1px solid white;
    margin-bottom: -1px;
}

.tab-close {
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    padding: 2px;
    border-radius: 3px;
    font-size: 16px;
    line-height: 1;
}

.tab-close:hover {
    background: #e9ecef;
    color: #495057;
}

.tab-content {
    flex: 1;
    overflow: hidden;
    position: relative;
}

.tab-pane {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    overflow: auto;
    display: none;
}

.tab-pane.active {
    display: block;
}

/* 欢迎页面 */
.welcome-page {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.welcome-content {
    text-align: center;
    color: #6c757d;
}

.welcome-content h2 {
    font-size: 24px;
    margin-bottom: 10px;
    color: #495057;
}

/* 图标样式 */
.icon-user::before { content: "👤"; }
.icon-arrow-down::before { content: "▼"; }
.icon-database::before { content: "🗄️"; }
.icon-chevron-down::before { content: "▼"; }
.icon-wave::before { content: "〰️"; }
.icon-sound::before { content: "🔊"; }
.icon-noise::before { content: "📢"; }
.icon-vibration::before { content: "📳"; }
.icon-search::before { content: "🔍"; }
.icon-file::before { content: "📄"; }
.icon-library::before { content: "📚"; }
.icon-book::before { content: "📖"; }
.icon-download::before { content: "⬇️"; }
.icon-empty::before { content: "📭"; }

/* 响应式设计 */
@media (max-width: 768px) {
    .app-sidebar {
        width: 200px;
    }

    .app-title {
        font-size: 16px;
    }

    .nav-link {
        padding: 10px 15px 10px 30px;
        font-size: 13px;
    }
}
