/* 模态数据页面样式 */
.modal-data-container {
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 20px;
    gap: 20px;
}

/* 搜索面板 */
.search-panel {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    overflow: hidden;
}

.search-tabs {
    display: flex;
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.tab-btn {
    flex: 1;
    padding: 12px 20px;
    background: none;
    border: none;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    color: #6c757d;
    transition: all 0.2s;
}

.tab-btn:hover {
    background: #e9ecef;
}

.tab-btn.active {
    background: white;
    color: #495057;
    border-bottom: 2px solid #007bff;
}

.search-content {
    padding: 20px;
    display: none;
}

.search-content.active {
    display: block;
}

.search-form {
    max-width: 100%;
}

.form-row {
    display: flex;
    gap: 20px;
    align-items: end;
    flex-wrap: wrap;
}

.form-group {
    flex: 1;
    min-width: 200px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #495057;
}

.form-control {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.2s;
}

.form-control:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
}

.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    display: inline-flex;
    align-items: center;
    gap: 5px;
}

.btn-primary {
    background: #007bff;
    color: white;
}

.btn-primary:hover {
    background: #0056b3;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #545b62;
}

/* 结果面板 */
.result-panel {
    flex: 1;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.result-header {
    padding: 15px 20px;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.result-header h3 {
    margin: 0;
    font-size: 16px;
    color: #495057;
}

.result-actions {
    display: flex;
    gap: 10px;
}

.result-content {
    flex: 1;
    overflow: auto;
    padding: 20px;
}

.no-data {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: #6c757d;
}

.no-data i {
    font-size: 48px;
    margin-bottom: 10px;
}

/* 数据表格 */
.data-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
}

.data-table th,
.data-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #dee2e6;
}

.data-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #495057;
    position: sticky;
    top: 0;
    z-index: 10;
}

.data-table tr:hover {
    background: #f8f9fa;
}

.frequency-link {
    color: #007bff;
    cursor: pointer;
    text-decoration: underline;
}

.frequency-link:hover {
    color: #0056b3;
}

/* 模态弹窗 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.5);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 2000;
}

.modal-overlay.show {
    display: flex;
}

.modal-dialog {
    background: white;
    border-radius: 8px;
    width: 90%;
    max-width: 800px;
    max-height: 90%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.modal-header {
    padding: 15px 20px;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h4 {
    margin: 0;
    font-size: 16px;
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #6c757d;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-close:hover {
    color: #495057;
}

.modal-body {
    flex: 1;
    overflow: auto;
    padding: 20px;
}

.modal-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 6px;
}

.info-item {
    display: flex;
    gap: 10px;
}

.info-item label {
    font-weight: 600;
    color: #495057;
    min-width: 60px;
}

.modal-content-area {
    border: 1px solid #dee2e6;
    border-radius: 6px;
    overflow: hidden;
}

.modal-tabs {
    display: flex;
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.modal-tab {
    flex: 1;
    padding: 10px 15px;
    background: none;
    border: none;
    cursor: pointer;
    font-size: 14px;
    color: #6c757d;
    transition: all 0.2s;
}

.modal-tab:hover {
    background: #e9ecef;
}

.modal-tab.active {
    background: white;
    color: #495057;
    border-bottom: 2px solid #007bff;
}

.modal-tab-content {
    position: relative;
    min-height: 400px;
}

.tab-pane {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    padding: 20px;
    display: none;
}

.tab-pane.active {
    display: block;
}

.shape-viewer,
.photo-viewer {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
}

.shape-viewer img,
.photo-viewer img {
    max-width: 100%;
    max-height: 100%;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .modal-data-container {
        padding: 10px;
        gap: 15px;
    }

    .form-row {
        flex-direction: column;
        gap: 15px;
    }

    .form-group {
        min-width: auto;
    }

    .modal-dialog {
        width: 95%;
        margin: 10px;
    }

    .modal-info {
        grid-template-columns: 1fr;
        gap: 10px;
    }
}
