
# NVH数据管理系统需求分析文档

## 1. 项目概述

### 1.1 项目背景
NVH（Noise, Vibration, and Harshness）数据管理系统旨在解决当前NVH测试数据和仿真数据管理中的痛点，提供一个统一的数据管理、查询、分析和对比平台。

### 1.2 项目目标
- 建立统一的NVH数据管理平台
- 提供便捷的数据查询和对比功能
- 支持在线数据处理和声音回放
- 实现数据的标准化管理和存储

### 1.3 技术栈
- **后端**: Flask + Python
- **前端**: HTML + JavaScript + CSS
- **模板引擎**: Jinja2
- **数据库**: MySQL
- **认证**: Keycloak

## 2. 业务需求分析

### 2.1 当前业务痛点

#### 2.1.1 数据查询痛点
1. **报告数据分散**: 数据以PPT、原始数据为主，部分为Excel，分散在不同人员电脑上
2. **数据查找困难**: 没有出过报告的数据难以查找，报告数据在他人电脑上无法访问
3. **对比分析困难**: 列表式数据可以复制对比，但曲线图无法在同一图表中对比
4. **数据处理复杂**: 需要拷贝原始数据进行后处理，重新生成对比图表
5. **参数设置不一致**: 采样率、转速、车速设置不同，导致数据无法在同一图表中显示
6. **声音回放受限**: 需要专业软件才能回放声音

#### 2.1.2 数据管理痛点
1. **数据格式不统一**: 数据以多种格式存在（PPT、Excel、原始数据文件）
2. **数据关联性差**: 零件与整车、各参数之间的关联关系不清晰
3. **数据更新不及时**: 缺乏统一的数据更新机制
4. **权限管理缺失**: 没有统一的用户权限管理

### 2.2 预期改进效果

#### 2.2.1 数据查询改进
1. **统一数据源**: 所有NVH数据集中管理，便于查找
2. **在线对比分析**: 支持多种数据在同一图表中进行对比
3. **快速数据处理**: 提供在线数据处理功能，无需下载原始数据
4. **声音在线回放**: 支持在系统中直接回放声音文件

#### 2.2.2 数据管理改进
1. **标准化存储**: 统一数据格式和存储标准
2. **关联关系管理**: 建立清晰的数据关联关系
3. **权限控制**: 基于Keycloak的统一认证和权限管理
4. **数据追溯**: 完整的数据更新和版本管理

## 3. 功能需求分析

### 3.1 核心功能模块

#### 3.1.1 模态数据管理
**功能描述**: 管理车辆和零部件的模态测试数据

**具体需求**:
1. **按车型搜索**:
   - 选择车型
   - 选择零件或全选
   - 生成模态分布表（分类表格形式）
   - 点击频率查看模态振型（GIF动图）

2. **按零件搜索**:
   - 选择车型
   - 选择状态（整车状态、零件自由状态、零件约束状态）
   - 选择阶次或所有阶次
   - 生成模态分布散点图（横轴车型，纵轴模态类型）
   - 点击散点查看模态振型（GIF）、测试照片（JPG等）

**模态分布表结构**:
- **底盘系统**: 悬架、后副车架、传动系、动力总成、排气系统、转向系统、燃油冷却
- **车身系统**: 车身、车身局部、车身空腔模态、座椅、仪表盘、附件、开闭件
- **显示内容**: 分类、频率（Hz）、具体模态类型

#### 3.1.2 气密性数据管理
**功能描述**: 管理白车身气密性、整车气密性、整车声密性数据

**数据类型**:
- 白车身气密性测试数据
- 整车气密性测试数据
- 整车声密性测试数据

#### 3.1.3 吸隔声数据管理
**功能描述**: 管理各种吸隔声测试数据

**数据类型**:
- 空隙率数据
- 流阻数据
- 垂直入射法吸声数据
- 垂直入射法隔声数据
- 上墙法隔声数据
- 整车隔声量数据
- 整车混响时间数据
- 整车隔区域隔声量数据

#### 3.1.4 传函数据管理
**功能描述**: 管理NFT（Noise Transfer Function）和VTF（Vibration Transfer Function）数据

#### 3.1.5 动刚度和频响数据管理
**功能描述**: 管理动刚度和频响测试数据

#### 3.1.6 隔振率数据管理
**功能描述**: 管理各种隔振率测试数据

**数据类型**:
- 动力总成悬置隔振率
- 进排气隔振率
- 悬架隔振率

#### 3.1.7 异响数据管理
**功能描述**: 管理异响测试数据

**数据类型**:
- 整车异响数据
- 零部件异响数据
- 材料异响数据

#### 3.1.8 声品质数据管理
**功能描述**: 管理声品质测试数据

**数据类型**:
- 车门声品质数据
- 雨刮声品质数据
- 玻璃升降器声品质数据
- 提示音声品质数据

#### 3.1.9 振动噪声数据管理
**功能描述**: 管理振动噪声测试数据

**数据类型**:
- 动力传动振动噪声
- 路噪数据
- 风噪数据
- 热管理振动噪声
- 零部件单体振动噪声

### 3.2 通用功能需求

#### 3.2.1 用户认证与权限管理
- 基于Keycloak的统一认证

#### 3.2.2 数据查询与搜索
- 多维度数据搜索
- 高级筛选功能
- 数据对比分析
- 历史数据追溯

#### 3.2.3 数据可视化
- 图表展示功能
- 曲线图对比
- 模态振型可视化（GIF动图展示）
- 散点图展示（模态分布图）
- 频谱图和时域图
- 数据统计图表（最大值、最小值、平均值等）

#### 3.2.4 数据处理
- 在线数据处理
- 数据格式转换
- 参数标准化
- 数据导出功能
- 频谱分析和时域分析
- 数据对比分析（多车型数据在同一图表中显示）
- 统计分析（最大值、最小值、平均值等）

#### 3.2.5 声音回放
- 在线声音播放
- 音频文件管理
- 声音对比分析

## 4. 数据需求分析

### 4.1 数据来源
- **测试数据**: NVH实验室测试数据
- **仿真数据**: 仿真分析结果
- **经验库数据**: 历史项目数据
- **对标数据**: 170台对标车数据
- **公司车型数据**: 公司内部车型数据

### 4.2 数据量估算
- **对标车数据**: 170台
- **公司车型数据**: 持续增加
- **测试数据**: 大量历史测试数据
- **仿真数据**: 持续生成的仿真结果

### 4.3 数据类型和格式
- **模态数据**: 
  - 频率、阻尼比等数值数据（CSV格式，几MB）
  - 模态振型文件（GIF动图）
  - 测试照片（JPG、PNG等图片格式）
- **噪声数据**: 
  - 原始数据（CSV格式，上百万条数据，几MB）
  - 主要用于频谱分析、时域分析、声音回放
- **其他数据**: 各种测试结果的数值和图表数据

### 4.3 数据更新频率
- **测试数据**: 根据测试计划更新
- **仿真数据**: 根据项目进度更新
- **经验库**: 项目完成后更新

### 4.4 数据关联关系
- 车型与零部件关联
- 测试参数之间关联
- 测试数据与仿真数据关联
- 历史数据与当前项目关联


3.1 后端模块结构
3.1 后端模块结构
```
nvhDemo/
├── app.py                 # 应用入口
├── config.py             # 配置文件
├── decorators.py             # 配置文件
├── requirements.txt      # 依赖包
├── models/              # 数据模型
│   ├── __init__.py
│   ├── vehicle.py
├── controllers/         # 控制器
│   ├── __init__.py
├── services/           # 业务逻辑
│   ├── __init__.py
├── utils/             # 工具类
│   └── response.py
├── static/           # 静态文件
│   ├── css/
│   ├── js/
│   ├── images/
└── templates/        # 模板文件
    ├── base.html
    ├── index.html
    ├── modal/

```

### 3.2 前端模块结构
```
static/
├── css/
├── js/
│   └── utils.js         # 工具函数
└── images/
    ├── icons/
    └── logos/
```
