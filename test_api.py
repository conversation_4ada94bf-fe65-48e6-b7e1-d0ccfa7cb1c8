#!/usr/bin/env python3
"""
直接测试API功能
"""

from services.modal_service import ModalService

def test_modal_service():
    """测试模态服务"""
    print("=== 测试模态服务 ===")
    
    service = ModalService()
    
    # 测试获取车型
    print("\n1. 测试获取车型...")
    vehicles_result = service.get_vehicles()
    print(f"车型结果类型: {type(vehicles_result)}")
    print(f"车型结果: {vehicles_result}")
    
    # 测试获取零部件
    print("\n2. 测试获取零部件...")
    components_result = service.get_components()
    print(f"零部件结果类型: {type(components_result)}")
    print(f"零部件结果: {components_result}")
    
    # 测试按车型搜索
    print("\n3. 测试按车型搜索...")
    search_result = service.search_by_vehicle('SGM001')
    print(f"搜索结果类型: {type(search_result)}")
    print(f"搜索结果: {search_result}")
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    test_modal_service()
