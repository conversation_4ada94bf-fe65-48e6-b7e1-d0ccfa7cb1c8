##  1.技术架构

- **前端**: HTML5 + CSS3 + JavaScript (ES6+)
- **后端**: Flask 2.x + Python 3.8+
- **数据库**: MySQL 8.0+
- **认证**: Keycloak
- **文件存储**: 本地文件系统
- **部署**:  Nginx
## 2. 数据库设计


### 2.1 核心数据表结构

#### 2.1.1 车型信息表 (vehicle_models)
```sql
CREATE TABLE vehicle_models (
    id INT PRIMARY KEY AUTO_INCREMENT,
    vehicle_model_code VARCHAR(50) UNIQUE NOT NULL,        -- 车型代码
    vehicle_model_name VARCHAR(100) NOT NULL,              -- 车型名称
    vin VARCHAR(50) UNIQUE NOT NULL,               -- VIN码
    drive_type VARCHAR(30),                        -- 驱动类型
    configuration VARCHAR(200),                    -- 具体配置
    production_year INT,                           -- 生产年份
    status ENUM('active', 'inactive') DEFAULT 'active'
);
```


#### 2.1.2 零部件表 (components)
```sql
CREATE TABLE components (
    id INT PRIMARY KEY AUTO_INCREMENT,
    component_code VARCHAR(50) UNIQUE NOT NULL,    -- 零件代码
    component_name VARCHAR(100) NOT NULL,          -- 零件名称
    category VARCHAR(100) NOT NULL,    -- 主分类（底盘系统、车身系统等）
    sub_category VARCHAR(50) NOT NULL,             -- 子分类（悬架、副车架、动力总成、排气等）
    parent_id INT,                                 -- 父级零件ID(用于层级结构)
    description TEXT,                              -- 描述
    material VARCHAR(100),                         -- 材料
    weight DECIMAL(8,3),                          -- 重量(kg)
    FOREIGN KEY (parent_id) REFERENCES components(id)
);
```

#### 2.1.3 测试项目表 (test_projects)
模态数据标签：车型、VIN、阶段、配置、生产年份、零件名称、零件测试边界条件、模态阶次、激励方式、测试人员、分析人员、创建日期、修改日期、修改人员、车辆类型分类
```sql
CREATE TABLE test_projects (
    id INT PRIMARY KEY AUTO_INCREMENT,
    project_code VARCHAR(50) UNIQUE NOT NULL,      -- 项目代码
    project_name VARCHAR(100) NOT NULL,            -- 项目名称
    vehicle_model_id INT NOT NULL,              -- 车辆ID
    component_id INT,                              -- 零件ID(可选，整车测试时为空；待定)
    test_type VARCHAR(200) NOT NULL,  -- 测试类型(模态、刚度、噪声、振动等)
    test_date DATE NOT NULL,                       -- 测试日期
    test_location VARCHAR(100),                    -- 测试地点
    test_engineer VARCHAR(50) NOT NULL,            -- 测试工程师
    test_condition VARCHAR(200),  -- 测试条件(工况)
    test_status VARCHAR(200),          -- 测试状态（整车状态）
    excitation_method VARCHAR(100),                -- 激励方式
    notes TEXT,                                    -- 备注
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (vehicle_models_id) REFERENCES vehicle_models(id),
    FOREIGN KEY (component_id) REFERENCES components(id)
);
```

#### 2.1.4 模态数据表 (modal_data)
```sql
CREATE TABLE modal_data (
    id INT PRIMARY KEY AUTO_INCREMENT,
    test_project_id INT NOT NULL,                  -- 测试项目ID
    mode_order INT NOT NULL,                       -- 模态阶次
    direcation VARCHAR(100) ,               -- 方向（X Y Z）
    frequency DECIMAL(10,2) NOT NULL,              -- 频率(Hz)
    damping_ratio DECIMAL(6,2),                    -- 阻尼比
    mode_shape_description TEXT,                   -- 模态振型描述（花瓣、弯曲、扭转）
    mode_shape_file VARCHAR(255),                  -- GIF动图文件路径
    test_photo_file VARCHAR(255),                  -- 测试照片文件路径
    notes TEXT,                                    -- 备注
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    updated_by VARCHAR(50),                        -- 修改人员
    FOREIGN KEY (test_project_id) REFERENCES test_projects(id)
);
```

#### 2.1.6 气密性数据表 (airtightness_data)
```sql
CREATE TABLE airtightness_data (
    id INT PRIMARY KEY AUTO_INCREMENT,
    test_project_id INT NOT NULL,                  -- 测试项目ID
    airtightness_type ENUM('body_in_white', 'vehicle', 'sound_tightness') NOT NULL,
    pressure_difference DECIMAL(8,2),              -- 压差(Pa)
    air_flow_rate DECIMAL(10,4),                   -- 空气流量(m³/h)
    leakage_area DECIMAL(8,4),                     -- 泄漏面积(cm²)
    test_data_file VARCHAR(255),                   -- 测试数据文件
    notes TEXT,                                    -- 备注
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    updated_by VARCHAR(50),                        -- 修改人员
    FOREIGN KEY (test_project_id) REFERENCES test_projects(id)
);
```



