// 主应用类
class NVHApp {
    constructor() {
        this.tabs = new Map(); // 存储已打开的标签页
        this.activeTab = null; // 当前活动标签页
        this.init();
    }

    // 初始化应用
    init() {
        this.initEventListeners();
        this.initUserInfo();
        this.initSidebar();
    }

    // 初始化事件监听
    initEventListeners() {
        // 侧边栏菜单点击
        document.addEventListener('click', (e) => {
            // 菜单项点击
            if (e.target.closest('.nav-link')) {
                e.preventDefault();
                this.handleMenuClick(e.target.closest('.nav-link'));
            }

            // 菜单组折叠/展开
            if (e.target.closest('.nav-group-title')) {
                this.toggleNavGroup(e.target.closest('.nav-group'));
            }

            // 标签页点击
            if (e.target.closest('.tab-item')) {
                this.switchTab(e.target.closest('.tab-item').dataset.tabId);
            }

            // 标签页关闭
            if (e.target.closest('.tab-close')) {
                e.stopPropagation();
                this.closeTab(e.target.closest('.tab-item').dataset.tabId);
            }

            // 用户下拉菜单
            if (e.target.closest('.dropdown-btn')) {
                this.toggleUserDropdown();
            }

            // 点击其他地方关闭下拉菜单
            if (!e.target.closest('.user-dropdown')) {
                this.closeUserDropdown();
            }
        });

        // 键盘事件
        document.addEventListener('keydown', (e) => {
            // Ctrl+W 关闭当前标签页
            if (e.ctrlKey && e.key === 'w') {
                e.preventDefault();
                if (this.activeTab) {
                    this.closeTab(this.activeTab);
                }
            }
        });
    }

    // 初始化用户信息
    async initUserInfo() {
        try {
            const response = await api.get('/user/info');
            const user = response.data;

            const userNameEl = document.querySelector('.user-name');
            if (userNameEl && user) {
                userNameEl.textContent = user.name || user.preferred_username || '用户';
            }
        } catch (error) {
            console.error('获取用户信息失败:', error);
        }
    }

    // 初始化侧边栏
    initSidebar() {
        // 默认展开第一个菜单组
        const firstGroup = document.querySelector('.nav-group');
        if (firstGroup) {
            firstGroup.classList.add('expanded');
        }
    }

    // 处理菜单点击
    handleMenuClick(menuLink) {
        const module = menuLink.dataset.module;
        const title = menuLink.dataset.title;

        if (!module || !title) return;

        // 移除其他菜单的活动状态
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
        });

        // 添加当前菜单的活动状态
        menuLink.classList.add('active');

        // 打开或切换到对应标签页
        this.openTab(module, title);
    }

    // 打开标签页
    async openTab(module, title) {
        const tabId = module;

        // 如果标签页已存在，直接切换
        if (this.tabs.has(tabId)) {
            this.switchTab(tabId);
            return;
        }

        try {
            // 创建标签页
            this.createTab(tabId, title);

            // 加载内容
            await this.loadTabContent(tabId, module);

            // 切换到新标签页
            this.switchTab(tabId);

            // 隐藏欢迎页面
            const welcomePage = document.getElementById('welcomePage');
            if (welcomePage) {
                welcomePage.style.display = 'none';
            }
        } catch (error) {
            console.error('打开标签页失败:', error);
            Utils.showMessage('打开页面失败', 'error');
        }
    }

    // 创建标签页
    createTab(tabId, title) {
        const tabHeader = document.getElementById('tabHeader');
        const tabContent = document.getElementById('tabContent');

        // 创建标签页头部
        const tabItem = document.createElement('div');
        tabItem.className = 'tab-item';
        tabItem.dataset.tabId = tabId;
        tabItem.innerHTML = `
            <span class="tab-title">${title}</span>
            <button class="tab-close">&times;</button>
        `;

        // 创建标签页内容
        const tabPane = document.createElement('div');
        tabPane.className = 'tab-pane';
        tabPane.id = `tab-${tabId}`;
        tabPane.innerHTML = '<div class="tab-loading">加载中...</div>';

        tabHeader.appendChild(tabItem);
        tabContent.appendChild(tabPane);

        // 存储标签页信息
        this.tabs.set(tabId, {
            title,
            element: tabPane,
            headerElement: tabItem
        });
    }

    // 加载标签页内容
    async loadTabContent(tabId, module) {
        const tabPane = document.getElementById(`tab-${tabId}`);
        if (!tabPane) return;

        const loading = Utils.showLoading(tabPane, '加载页面内容...');

        try {
            let content = '';

            switch (module) {
                case 'modal':
                    content = await this.loadModalContent();
                    break;
                case 'sound-quality':
                    content = '<div class="coming-soon">声品质功能开发中...</div>';
                    break;
                case 'noise':
                    content = '<div class="coming-soon">噪声功能开发中...</div>';
                    break;
                case 'vibration':
                    content = '<div class="coming-soon">振动功能开发中...</div>';
                    break;
                default:
                    content = '<div class="coming-soon">功能开发中...</div>';
            }

            tabPane.innerHTML = content;

            // 如果是模态数据页面，初始化相关功能
            if (module === 'modal') {
                this.initModalPage();
            }

        } catch (error) {
            tabPane.innerHTML = '<div class="error-message">加载失败，请重试</div>';
            throw error;
        } finally {
            Utils.hideLoading(tabPane);
        }
    }

    // 加载模态数据内容
    async loadModalContent() {
        // 这里应该从服务器获取模板，暂时返回静态内容
        return `
            <div class="modal-data-container">
                <div class="search-panel">
                    <div class="search-tabs">
                        <button class="tab-btn active" data-tab="vehicle">按车型搜索</button>
                        <button class="tab-btn" data-tab="component">按零件搜索</button>
                    </div>

                    <!-- 按车型搜索 -->
                    <div class="search-content active" id="vehicleSearch">
                        <div class="search-form">
                            <div class="form-row">
                                <div class="form-group">
                                    <label>车型选择</label>
                                    <select id="vehicleSelect" class="form-control">
                                        <option value="">请选择车型</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label>零件选择</label>
                                    <select id="componentSelect" class="form-control">
                                        <option value="">请选择零件</option>
                                        <option value="all">全选</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <button class="btn btn-primary" onclick="modalManager.searchByVehicle()">
                                        <i class="icon-search"></i>
                                        搜索
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 按零件搜索 -->
                    <div class="search-content" id="componentSearch">
                        <div class="search-form">
                            <div class="form-row">
                                <div class="form-group">
                                    <label>车型选择</label>
                                    <select id="vehicleSelect2" class="form-control">
                                        <option value="">请选择车型</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label>状态选择</label>
                                    <select id="statusSelect" class="form-control">
                                        <option value="">请选择状态</option>
                                        <option value="vehicle">整车状态</option>
                                        <option value="free">零件自由状态</option>
                                        <option value="constrained">零件约束状态</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label>阶次选择</label>
                                    <select id="orderSelect" class="form-control">
                                        <option value="">请选择阶次</option>
                                        <option value="all">所有阶次</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <button class="btn btn-primary" onclick="modalManager.searchByComponent()">
                                        <i class="icon-search"></i>
                                        搜索
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 结果展示区域 -->
                <div class="result-panel">
                    <div class="result-header">
                        <h3>搜索结果</h3>
                        <div class="result-actions">
                            <button class="btn btn-secondary" onclick="modalManager.exportData()">
                                <i class="icon-download"></i>
                                导出数据
                            </button>
                        </div>
                    </div>

                    <div class="result-content" id="resultContent">
                        <div class="no-data" id="noData">
                            <i class="icon-empty"></i>
                            <p>暂无数据，请先进行搜索</p>
                        </div>
                    </div>
                </div>

                <!-- 模态振型查看弹窗 -->
                <div class="modal-overlay" id="modalOverlay">
                    <div class="modal-dialog">
                        <div class="modal-header">
                            <h4>模态振型查看</h4>
                            <button class="modal-close" onclick="modalManager.closeModal()">&times;</button>
                        </div>
                        <div class="modal-body">
                            <div class="modal-info">
                                <div class="info-item">
                                    <label>车型:</label>
                                    <span id="modalVehicle">-</span>
                                </div>
                                <div class="info-item">
                                    <label>零件:</label>
                                    <span id="modalComponent">-</span>
                                </div>
                                <div class="info-item">
                                    <label>频率:</label>
                                    <span id="modalFrequency">-</span>
                                </div>
                                <div class="info-item">
                                    <label>阶次:</label>
                                    <span id="modalOrder">-</span>
                                </div>
                            </div>
                            <div class="modal-content-area">
                                <div class="modal-tabs">
                                    <button class="modal-tab active" data-tab="shape">振型动画</button>
                                    <button class="modal-tab" data-tab="photo">测试照片</button>
                                </div>
                                <div class="modal-tab-content">
                                    <div class="tab-pane active" id="shapePane">
                                        <div class="shape-viewer">
                                            <img id="shapeGif" src="" alt="模态振型" />
                                        </div>
                                    </div>
                                    <div class="tab-pane" id="photoPane">
                                        <div class="photo-viewer">
                                            <img id="testPhoto" src="" alt="测试照片" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    // 初始化模态数据页面
    initModalPage() {
        // 初始化搜索标签页切换
        document.addEventListener('click', (e) => {
            if (e.target.matches('.tab-btn')) {
                this.switchSearchTab(e.target);
            }
            if (e.target.matches('.modal-tab')) {
                this.switchModalTab(e.target);
            }
        });

        // 初始化模态数据管理器
        if (typeof ModalManager !== 'undefined') {
            window.modalManager = new ModalManager();
        }
    }

    // 切换搜索标签页
    switchSearchTab(tabBtn) {
        const tab = tabBtn.dataset.tab;

        // 更新按钮状态
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        tabBtn.classList.add('active');

        // 更新内容显示
        document.querySelectorAll('.search-content').forEach(content => {
            content.classList.remove('active');
        });

        const targetContent = document.getElementById(`${tab}Search`);
        if (targetContent) {
            targetContent.classList.add('active');
        }
    }

    // 切换模态弹窗标签页
    switchModalTab(tabBtn) {
        const tab = tabBtn.dataset.tab;

        // 更新按钮状态
        document.querySelectorAll('.modal-tab').forEach(btn => {
            btn.classList.remove('active');
        });
        tabBtn.classList.add('active');

        // 更新内容显示
        document.querySelectorAll('#modalOverlay .tab-pane').forEach(pane => {
            pane.classList.remove('active');
        });

        const targetPane = document.getElementById(`${tab}Pane`);
        if (targetPane) {
            targetPane.classList.add('active');
        }
    }

    // 切换标签页
    switchTab(tabId) {
        if (!this.tabs.has(tabId)) return;

        // 更新标签页头部状态
        document.querySelectorAll('.tab-item').forEach(item => {
            item.classList.remove('active');
        });

        const tabItem = this.tabs.get(tabId).headerElement;
        tabItem.classList.add('active');

        // 更新标签页内容状态
        document.querySelectorAll('.tab-pane').forEach(pane => {
            pane.classList.remove('active');
        });

        const tabPane = this.tabs.get(tabId).element;
        tabPane.classList.add('active');

        this.activeTab = tabId;
    }

    // 关闭标签页
    closeTab(tabId) {
        if (!this.tabs.has(tabId)) return;

        const tab = this.tabs.get(tabId);

        // 移除DOM元素
        tab.headerElement.remove();
        tab.element.remove();

        // 从Map中删除
        this.tabs.delete(tabId);

        // 如果关闭的是当前活动标签页
        if (this.activeTab === tabId) {
            // 切换到其他标签页或显示欢迎页面
            const remainingTabs = Array.from(this.tabs.keys());
            if (remainingTabs.length > 0) {
                this.switchTab(remainingTabs[remainingTabs.length - 1]);
            } else {
                this.activeTab = null;
                const welcomePage = document.getElementById('welcomePage');
                if (welcomePage) {
                    welcomePage.style.display = 'flex';
                }
            }
        }
    }

    // 切换菜单组展开/折叠
    toggleNavGroup(navGroup) {
        navGroup.classList.toggle('collapsed');
    }

    // 切换用户下拉菜单
    toggleUserDropdown() {
        const dropdown = document.querySelector('.dropdown-menu');
        if (dropdown) {
            dropdown.classList.toggle('show');
        }
    }

    // 关闭用户下拉菜单
    closeUserDropdown() {
        const dropdown = document.querySelector('.dropdown-menu');
        if (dropdown) {
            dropdown.classList.remove('show');
        }
    }
}

// 页面加载完成后初始化应用
document.addEventListener('DOMContentLoaded', () => {
    window.nvhApp = new NVHApp();
});

// 添加一些通用样式
const additionalStyles = `
    .coming-soon {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 300px;
        color: #6c757d;
        font-size: 16px;
        background: #f8f9fa;
        border-radius: 8px;
        margin: 20px;
    }
    
    .error-message {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 300px;
        color: #dc3545;
        font-size: 16px;
        background: #f8f9fa;
        border-radius: 8px;
        margin: 20px;
    }
    
    .tab-loading {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 300px;
        color: #6c757d;
        font-size: 16px;
    }
`;

const styleSheet = document.createElement('style');
styleSheet.textContent = additionalStyles;
document.head.appendChild(styleSheet);
