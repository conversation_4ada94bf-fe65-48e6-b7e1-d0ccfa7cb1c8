# keycloak 实现方案
```python
# config.py
class Config:
    SECRET_KEY = 'your-secret-key-here'

    # 前端认证客户端
    KEYCLOAK_FRONTEND_CLIENT_ID = 'front'
    KEYCLOAK_FRONTEND_CLIENT_SECRET = 'frontend-secret'

    # 后端API客户端（bearer-only）
    KEYCLOAK_BACKEND_CLIENT_ID = 'backend'
    KEYCLOAK_BACKEND_CLIENT_SECRET = '8545c061-7cf7-41e5-b92b-e6769a6a75b8'

    KEYCLOAK_SERVER_METADATA_URL = 'https://account-test.sgmw.com.cn/auth/realms/demo/.well-known/openid-configuration'
    KEYCLOAK_CLIENT_KWARGS = {
        'scope': 'openid email profile'
    }
    
from flask import Flask, render_template, session, redirect, url_for, request
from authlib.integrations.flask_client import OAuth
from config import Config
from utils.result import Result
from functools import wraps
from decorators import login_required, api_login_required

app = Flask(__name__)
app.config.from_object(Config)

# 初始化OAuth
oauth = OAuth(app)

# 注册Keycloak客户端
keycloak = oauth.register(
    name='keycloak',
    client_id=app.config['KEYCLOAK_FRONTEND_CLIENT_ID'],
    client_secret=app.config['KEYCLOAK_FRONTEND_CLIENT_SECRET'],
    server_metadata_url=app.config['KEYCLOAK_SERVER_METADATA_URL'],
    client_kwargs=app.config['KEYCLOAK_CLIENT_KWARGS'],
)


# ========== 页面路由 ==========
@app.route('/')
@login_required
def index():
    """主页 - 需要认证"""
    user = session.get('user')
    return render_template('index.html', user=user)


@app.route('/login')
def login():
    """登录"""
    if 'user' in session:
        return redirect(url_for('index'))

    redirect_uri = url_for('auth_callback', _external=True)
    return keycloak.authorize_redirect(redirect_uri)


@app.route('/auth/callback')
def auth_callback():
    """认证回调"""
    token = keycloak.authorize_access_token()
    user = token.get('userinfo')
    if user:
        session['user'] = user
        session['token'] = token
    return redirect(url_for('index'))


@app.route('/logout')
def logout():
    """登出"""
    session.clear()
    return redirect(url_for('login'))


# ========== API路由 ==========
@app.route('/api/user/info')
@api_login_required
def api_get_user_info():
    """获取当前用户信息"""
    user = session.get('user')
    return Result.success(user, "获取用户信息成功").to_json()


@app.route('/api/test')
@api_login_required
def api_test():
    """API测试接口"""
    test_data = {
        "timestamp": "2024-01-01 12:00:00",
        "server": "Flask Server",
        "status": "running"
    }
    return Result.success(test_data, "测试接口调用成功").to_json()


if __name__ == '__main__':
    app.run(debug=True, host='127.0.0.1', port=5000)

```

# 请求和响应规范
```python
# result.py
from flask import jsonify
from typing import Any, Optional, Dict
from enum import Enum


class StatusCode(Enum):
    SUCCESS = 200
    BAD_REQUEST = 400
    UNAUTHORIZED = 401
    FORBIDDEN = 403
    NOT_FOUND = 404
    INTERNAL_ERROR = 500


class Result:
    def __init__(self, code: int, message: str, data: Any = None):
        self.code = code
        self.message = message
        self.data = data

    def to_dict(self) -> Dict:
        result = {
            'code': self.code,
            'message': self.message,
            'success': self.code == StatusCode.SUCCESS.value
        }
        if self.data is not None:
            result['data'] = self.data
        return result

    def to_json(self):
        return jsonify(self.to_dict())

    @staticmethod
    def success(data: Any = None, message: str = "操作成功") -> 'Result':
        return Result(StatusCode.SUCCESS.value, message, data)

    @staticmethod
    def error(message: str = "操作失败", code: int = StatusCode.INTERNAL_ERROR.value) -> 'Result':
        return Result(code, message)

    @staticmethod
    def bad_request(message: str = "请求参数错误") -> 'Result':
        return Result(StatusCode.BAD_REQUEST.value, message)

    @staticmethod
    def unauthorized(message: str = "未授权访问") -> 'Result':
        return Result(StatusCode.UNAUTHORIZED.value, message)

    @staticmethod
    def forbidden(message: str = "禁止访问") -> 'Result':
        return Result(StatusCode.FORBIDDEN.value, message)

    @staticmethod
    def not_found(message: str = "资源不存在") -> 'Result':
        return Result(StatusCode.NOT_FOUND.value, message)

```

请求规范参考
```js
//request.js
class ApiRequest {
    constructor(baseURL = '/api') {
        this.baseURL = baseURL;
        this.defaultHeaders = {
            'Content-Type': 'application/json',
        };
    }

    // 通用请求方法
    async request(url, options = {}) {
        const config = {
            method: 'GET',
            headers: { ...this.defaultHeaders },
            ...options
        };

        // 如果是POST/PUT请求且有数据，转换为JSON
        if (config.body && typeof config.body === 'object') {
            config.body = JSON.stringify(config.body);
        }

        try {
            const response = await fetch(`${this.baseURL}${url}`, config);
            const result = await response.json();

            // 处理认证失败
            if (result.code === 401) {
                console.warn('认证失败，跳转到登录页');
                window.location.href = '/login';
                return Promise.reject(result);
            }

            // 统一处理响应
            if (result.code === 200) {
                return Promise.resolve(result);
            } else {
                return Promise.reject(result);
            }
        } catch (error) {
            console.error('请求失败:', error);
            return Promise.reject({
                code: 500,
                message: '网络请求失败',
                error: error.message
            });
        }
    }

    // GET请求
    get(url, params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const fullUrl = queryString ? `${url}?${queryString}` : url;
        return this.request(fullUrl);
    }

    // POST请求
    post(url, data = {}) {
        return this.request(url, {
            method: 'POST',
            body: data
        });
    }

    // PUT请求
    put(url, data = {}) {
        return this.request(url, {
            method: 'PUT',
            body: data
        });
    }

    // DELETE请求
    delete(url) {
        return this.request(url, {
            method: 'DELETE'
        });
    }
}

// 创建全局实例
const api = new ApiRequest();

// 使用示例函数
const ApiExamples = {
    // 获取用户信息
    async getUserInfo() {
        try {
            const response = await api.get('/user/info');
            console.log('用户信息:', response.data);
            return response.data;
        } catch (error) {
            console.error('获取用户信息失败:', error.message);
            throw error;
        }
    },

    // 测试API
    async testApi() {
        try {
            const response = await api.get('/test');
            console.log('测试结果:', response.data);
            return response.data;
        } catch (error) {
            console.error('API测试失败:', error.message);
            throw error;
        }
    }
};

```