// API请求封装
class ApiRequest {
    constructor(baseURL = '/api') {
        this.baseURL = baseURL;
        this.defaultHeaders = {
            'Content-Type': 'application/json',
        };
    }

    // 通用请求方法
    async request(url, options = {}) {
        const config = {
            method: 'GET',
            headers: { ...this.defaultHeaders },
            ...options
        };

        // 如果是POST/PUT请求且有数据，转换为JSON
        if (config.body && typeof config.body === 'object') {
            config.body = JSON.stringify(config.body);
        }

        try {
            const response = await fetch(`${this.baseURL}${url}`, config);
            const result = await response.json();

            // 处理认证失败
            if (result.code === 401) {
                console.warn('认证失败，跳转到登录页');
                window.location.href = '/login';
                return Promise.reject(result);
            }

            // 统一处理响应
            if (result.code === 200) {
                return Promise.resolve(result);
            } else {
                return Promise.reject(result);
            }
        } catch (error) {
            console.error('请求失败:', error);
            return Promise.reject({
                code: 500,
                message: '网络请求失败',
                error: error.message
            });
        }
    }

    // GET请求
    get(url, params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const fullUrl = queryString ? `${url}?${queryString}` : url;
        return this.request(fullUrl);
    }

    // POST请求
    post(url, data = {}) {
        return this.request(url, {
            method: 'POST',
            body: data
        });
    }

    // PUT请求
    put(url, data = {}) {
        return this.request(url, {
            method: 'PUT',
            body: data
        });
    }

    // DELETE请求
    delete(url) {
        return this.request(url, {
            method: 'DELETE'
        });
    }
}

// 创建全局实例
const api = new ApiRequest();

// 工具函数
const Utils = {
    // 显示消息提示
    showMessage(message, type = 'info') {
        const messageEl = document.createElement('div');
        messageEl.className = `message message-${type}`;
        messageEl.textContent = message;

        // 添加样式
        Object.assign(messageEl.style, {
            position: 'fixed',
            top: '20px',
            right: '20px',
            padding: '12px 20px',
            borderRadius: '4px',
            color: 'white',
            fontSize: '14px',
            zIndex: '9999',
            maxWidth: '300px',
            wordWrap: 'break-word'
        });

        // 根据类型设置背景色
        const colors = {
            success: '#28a745',
            error: '#dc3545',
            warning: '#ffc107',
            info: '#17a2b8'
        };
        messageEl.style.backgroundColor = colors[type] || colors.info;

        document.body.appendChild(messageEl);

        // 3秒后自动移除
        setTimeout(() => {
            if (messageEl.parentNode) {
                messageEl.parentNode.removeChild(messageEl);
            }
        }, 3000);
    },

    // 显示加载状态
    showLoading(element, text = '加载中...') {
        const loadingEl = document.createElement('div');
        loadingEl.className = 'loading-overlay';
        loadingEl.innerHTML = `
            <div class="loading-spinner">
                <div class="spinner"></div>
                <div class="loading-text">${text}</div>
            </div>
        `;

        // 添加样式
        const style = document.createElement('style');
        style.textContent = `
            .loading-overlay {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(255,255,255,0.8);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 1000;
            }
            .loading-spinner {
                text-align: center;
            }
            .spinner {
                width: 40px;
                height: 40px;
                border: 4px solid #f3f3f3;
                border-top: 4px solid #007bff;
                border-radius: 50%;
                animation: spin 1s linear infinite;
                margin: 0 auto 10px;
            }
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
            .loading-text {
                color: #666;
                font-size: 14px;
            }
        `;

        if (!document.querySelector('#loading-styles')) {
            style.id = 'loading-styles';
            document.head.appendChild(style);
        }

        element.style.position = 'relative';
        element.appendChild(loadingEl);
        return loadingEl;
    },

    // 隐藏加载状态
    hideLoading(element) {
        const loadingEl = element.querySelector('.loading-overlay');
        if (loadingEl) {
            loadingEl.remove();
        }
    },

    // 格式化日期
    formatDate(date, format = 'YYYY-MM-DD') {
        if (!date) return '-';
        const d = new Date(date);
        const year = d.getFullYear();
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const day = String(d.getDate()).padStart(2, '0');

        return format
            .replace('YYYY', year)
            .replace('MM', month)
            .replace('DD', day);
    },

    // 防抖函数
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
};
