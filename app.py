from flask import Flask, render_template, session, redirect, url_for, request
from authlib.integrations.flask_client import OAuth
from config import Config
from utils.response import Result
from decorators import login_required, api_login_required
from controllers.modal_controllers import modal_bp
app = Flask(__name__)
app.config.from_object(Config)

# 初始化OAuth
oauth = OAuth(app)

# 注册Keycloak客户端
keycloak = oauth.register(
    name='keycloak',
    client_id=app.config['KEYCLOAK_FRONTEND_CLIENT_ID'],
    client_secret=app.config['KEYCLOAK_FRONTEND_CLIENT_SECRET'],
    server_metadata_url=app.config['KEYCLOAK_SERVER_METADATA_URL'],
    client_kwargs=app.config['KEYCLOAK_CLIENT_KWARGS'],
)

# 注册蓝图
app.register_blueprint(modal_bp, url_prefix='/api/modal')

# ========== 页面路由 ==========
@app.route('/')
@login_required
def index():
    """主页 - 需要认证"""
    user = session.get('user')
    return render_template('index.html', user=user)


@app.route('/login')
def login():
    """登录"""
    if 'user' in session:
        return redirect(url_for('index'))

    redirect_uri = url_for('auth_callback', _external=True)
    return keycloak.authorize_redirect(redirect_uri)


@app.route('/auth/callback')
def auth_callback():
    """认证回调"""
    token = keycloak.authorize_access_token()
    user = token.get('userinfo')
    if user:
        session['user'] = user
        session['token'] = token
    return redirect(url_for('index'))


@app.route('/logout')
def logout():
    """登出"""
    session.clear()
    return redirect(url_for('login'))


# ========== API路由 ==========
@app.route('/api/user/info')
@api_login_required
def api_get_user_info():
    """获取当前用户信息"""
    user = session.get('user')
    return Result.success(user, "获取用户信息成功")


@app.route('/api/test')
@api_login_required
def api_test():
    """API测试接口"""
    test_data = {
        "timestamp": "2024-01-01 12:00:00",
        "server": "Flask Server",
        "status": "running"
    }
    return Result.success(test_data, "测试接口调用成功")


if __name__ == '__main__':
    app.run(debug=True, host='127.0.0.1', port=5000)
