from functools import wraps
from flask import session, redirect, url_for, request
from utils.response import Result

def login_required(f):
    """页面访问认证装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user' not in session:
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    return decorated_function

def api_login_required(f):
    """API访问认证装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user' not in session:
            return Result.unauthorized("请先登录")
        return f(*args, **kwargs)
    return decorated_function
