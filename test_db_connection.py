#!/usr/bin/env python3
"""
测试数据库连接
"""

def test_db_connection():
    try:
        from config import get_db
        print("正在测试数据库连接...")
        
        db = get_db()
        print("数据库连接成功！")
        
        # 测试简单查询
        from sqlalchemy import text
        result = db.execute(text("SELECT 1"))
        print("数据库查询测试成功！")
        
        db.close()
        print("数据库连接关闭成功！")
        
        return True
        
    except Exception as e:
        print(f"数据库连接失败: {e}")
        return False

def check_tables():
    try:
        from config import get_db
        from sqlalchemy import text
        
        db = get_db()
        
        # 检查表是否存在
        tables = ['vehicle_models', 'components', 'test_projects', 'modal_data']
        
        for table in tables:
            try:
                result = db.execute(text(f"SELECT COUNT(*) FROM {table}"))
                count = result.scalar()
                print(f"表 {table}: {count} 条记录")
            except Exception as e:
                print(f"表 {table}: 不存在或查询失败 - {e}")
        
        db.close()
        
    except Exception as e:
        print(f"检查表时出错: {e}")

if __name__ == "__main__":
    print("开始数据库连接测试...")
    
    if test_db_connection():
        print("\n检查数据库表...")
        check_tables()
    else:
        print("数据库连接失败，请检查数据库配置和服务状态")
