from flask import jsonify
from typing import Any
from enum import Enum


class StatusCode(Enum):
    SUCCESS = 200
    BAD_REQUEST = 400
    UNAUTHORIZED = 401
    FORBIDDEN = 403
    NOT_FOUND = 404
    INTERNAL_ERROR = 500


class Result:
    @staticmethod
    def result(code: int, message: str, data: Any = None):
        """统一返回方法，直接返回jsonify数据"""
        response = {
            'code': code,
            'message': message,
            'success': code == StatusCode.SUCCESS.value
        }
        if data is not None:
            response['data'] = data
        return jsonify(response)

    @staticmethod
    def success(data: Any = None, message: str = "操作成功"):
        return Result.result(StatusCode.SUCCESS.value, message, data)

    @staticmethod
    def error(message: str = "操作失败", code: int = StatusCode.INTERNAL_ERROR.value):
        return Result.result(code, message)

    @staticmethod
    def bad_request(message: str = "请求参数错误"):
        return Result.result(StatusCode.BAD_REQUEST.value, message)

    @staticmethod
    def unauthorized(message: str = "未授权访问"):
        return Result.result(StatusCode.UNAUTHORIZED.value, message)

    @staticmethod
    def forbidden(message: str = "禁止访问"):
        return Result.result(StatusCode.FORBIDDEN.value, message)

    @staticmethod
    def not_found(message: str = "资源不存在"):
        return Result.result(StatusCode.NOT_FOUND.value, message)
